<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { invoke } from "@tauri-apps/api/core";

const greetMsg = ref("");
const name = ref("");

async function greet() {
  // Learn more about <PERSON><PERSON> commands at https://tauri.app/develop/calling-rust/
  greetMsg.value = await invoke("greet", { name: name.value });
}

// 窗口全屏状态
const isFullscreen = ref(false);
// 防抖标志
const isTogglingFullscreen = ref(false);
// 保存窗口原始状态
const windowState = ref({
  x: 0,
  y: 0,
  width: 800,
  height: 600
});

// 切换最大化模式（使用窗口最大化替代全屏）
const maximizeWindow = async () => {
  // 防抖处理，避免频繁点击
  if (isTogglingFullscreen.value) {
    return;
  }
  
  try {
    isTogglingFullscreen.value = true;
    
    // 导入Tauri窗口API
    const { getCurrentWindow } = await import('@tauri-apps/api/window');
    const appWindow = getCurrentWindow();
    
    // 切换最大化模式
    const isCurrentlyMaximized = await appWindow.isMaximized();
    if (isCurrentlyMaximized) {
      await appWindow.unmaximize();
    } else {
      await appWindow.maximize();
    }
    
    // 更新状态
    isFullscreen.value = !isCurrentlyMaximized;
    
  } catch (error) {
    console.error('Toggle maximize error:', error);
  } finally {
    // 延迟释放防抖标志
    setTimeout(() => {
      isTogglingFullscreen.value = false;
    }, 100);
  }
};

// 最小化窗口
const minimizeWindow = async () => {
  try {
    // 调用Rust命令来最小化窗口
    const { invoke } = await import('@tauri-apps/api/core');
    await invoke('minimize_window');
  } catch (error) {
    console.error('Minimize window error:', error);
  }
};

// 关闭窗口
const closeWindow = async () => {
  try {
    // 调用Rust命令来关闭窗口
    const { invoke } = await import('@tauri-apps/api/core');
    console.log('Attempting to close window...');
    await invoke('close_window');
    console.log('Window closed successfully.');
  } catch (error) {
    console.error('Close window error:', error);
    // 如果在非Tauri环境中，尝试使用window.close()
    if (typeof (window as any).__TAURI__ === 'undefined') {
      console.log('Not in Tauri environment, trying window.close()...');
      window.close();
    }
  }
};

// 动态设置功能栏高度和展示区域尺寸
let updateSidebarHeight: () => void;
let updateShowcaseArea: () => void;

onMounted(() => {
  updateSidebarHeight = () => {
    const sidebar = document.querySelector('.sidebar') as HTMLElement;
    if (sidebar) {
      sidebar.style.height = `${window.innerHeight}px`;
    }
  };

  updateShowcaseArea = () => {
    const showcaseArea = document.querySelector('.showcase-area') as HTMLElement;
    if (showcaseArea) {
      showcaseArea.style.width = `${window.innerWidth}px`;
      showcaseArea.style.height = `${window.innerHeight}px`;
    }
  };

  // 初始设置
  updateSidebarHeight();
  updateShowcaseArea();

  // 监听窗口大小变化
  window.addEventListener('resize', updateSidebarHeight);
  window.addEventListener('resize', updateShowcaseArea);
});

// 清理事件监听器
onUnmounted(() => {
  if (updateSidebarHeight) {
    window.removeEventListener('resize', updateSidebarHeight);
  }
});
</script>

<template>
  <div class="background">
    <div class="sidebar">
      <div class="app-logo-container">
        <div class="app-logo"></div>
      </div>
    </div>
    <div class="title-bar" data-tauri-drag-region="true">
      <div class="window-controls">
        <div class="minimize-button" @click="minimizeWindow">
          <div class="minimize-icon"></div>
        </div>
        <div class="maximize-button" @click="maximizeWindow">
          <div class="maximize-icon"></div>
        </div>
        <div class="close-button" @click="closeWindow">
          <div class="close-icon"></div>
        </div>
      </div>
    </div>
    <div class="showcase-area">
      <main class="container">
        <h1>Welcome to Tauri + Vue</h1>

        <div class="row">
          <a href="https://vite.dev" target="_blank">
            <img src="/vite.svg" class="logo vite" alt="Vite logo" />
          </a>
          <a href="https://tauri.app" target="_blank">
            <img src="/tauri.svg" class="logo tauri" alt="Tauri logo" />
          </a>
          <a href="https://vuejs.org/" target="_blank">
            <img src="./assets/vue.svg" class="logo vue" alt="Vue logo" />
          </a>
        </div>
        <p>Click on the Tauri, Vite, and Vue logos to learn more.</p>

        <form class="row" @submit.prevent="greet">
          <input id="greet-input" v-model="name" placeholder="Enter a name..." />
          <button type="submit">Greet</button>
        </form>
        <p>{{ greetMsg }}</p>
      </main>
    </div>
  </div>
</template>

<style scoped>
.logo.vite:hover {
  filter: drop-shadow(0 0 2em #747bff);
}

.logo.vue:hover {
  filter: drop-shadow(0 0 2em #249b73);
}

</style>
<style>
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
  background-color: #000000 !important;
  border: none !important; /* 强制移除任何边框 */
}

:root {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;

  color: #f6f6f6;
  background-color: #000000;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

.background {
  width: 100vw;
  height: 100vh;
  background-color: #000000;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  /* 添加过渡效果 */
  transition: all 0.3s ease;
  /* 确保背景色优先级最高 */
  background-color: #000000 !important;
}

.sidebar {
  position: absolute;
  left: 0;
  top: 0;
  width: 55px;
  height: 100vh;
  background-color: #000000;
  border-right: 0.1px solid #3F3F3F;
  z-index: 10;
}

.app-logo-container {
  position: absolute;
  top: 22px; /* 根据 Figma 设计调整：20px + 2px 偏移 */
  left: 11px; /* 根据 Figma 设计调整 */
  width: 32px;
  height: 32px;
}

.app-logo {
  width: 32px;
  height: 32px;
  background-color: #5825BD;
  border-radius: 50%;
  position: relative;
}

.app-logo::after {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0) 100%);
  border-radius: 50%;
  z-index: -1;
}

.title-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 30px; /* Further reduced height */
  -webkit-app-region: drag;
  z-index: 15;
  background-color: transparent;
}


.window-controls {
  -webkit-app-region: no-drag;
  position: absolute;
  top: 7px; /* Adjusted for 16px buttons */
  right: 17.5px;
  z-index: 20;
  display: flex;
  gap: 10px; /* Space between buttons */
}

/* 最大化按钮 */
.maximize-button {
  width: 16px;
  height: 16px;
  background: linear-gradient(to bottom, #FFCC00 0%, #FFCC00 100%);
  border-radius: 50%;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  position: relative;
  cursor: pointer;
  z-index: 9999;
}

.maximize-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 9.65px; /* Adjusted for 16px button */
  height: 9.65px;
  border: 1px solid rgba(0, 0, 0, 0.8);
  box-sizing: border-box;
}

/* 最小化按钮 */
.minimize-button {
  width: 16px;
  height: 16px;
  background: linear-gradient(to bottom, #00FF55 0%, #00FF55 100%);
  border-radius: 50%;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  position: relative;
  cursor: pointer;
  z-index: 9999;
}

.minimize-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 12.71px; /* Adjusted for 16px button */
  height: 0px;
  border-top: 1px solid rgba(0, 0, 0, 0.8);
}

/* 关闭按钮 */
.close-button {
  width: 16px;
  height: 16px;
  background: linear-gradient(to bottom, #D83F3F 0%, #D83F3F 100%);
  border-radius: 50%;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  position: relative;
  cursor: pointer;
}

.close-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 9.05px; /* Adjusted for 16px button */
  height: 9.05px;
}

.close-icon::before,
.close-icon::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 1px;
  background-color: rgba(0, 0, 0, 0.8);
}

.close-icon::before {
  transform: translate(-50%, -50%) rotate(45deg);
}

.close-icon::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.showcase-area {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw; /* 使用视口宽度 */
  height: 100vh; /* 使用视口高度 */
  background: linear-gradient(to bottom, rgba(217, 217, 217, 0.15) 0%, rgba(255, 254, 254, 0) 100%);
  backdrop-filter: blur(80px);
  -webkit-backdrop-filter: blur(80px);
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  overflow: hidden;
  /* 添加过渡效果 */
  transition: all 0.3s ease;
}

.container {
  margin: 0;
  padding-top: 10vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: 0.75s;
}

.logo.tauri:hover {
  filter: drop-shadow(0 0 2em #24c8db);
}

.row {
  display: flex;
  justify-content: center;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}

a:hover {
  color: #535bf2;
}

h1 {
  text-align: center;
}

input,
button {
  border-radius: 8px;
  border: 1px solid #3c3c3c;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  color: #f6f6f6;
  background-color: #1a1a1a;
  transition: border-color 0.25s;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
}

button {
  cursor: pointer;
}

button:hover {
  border-color: #535bf2;
}
button:active {
  border-color: #535bf2;
  background-color: #2a2a2a;
}

input,
button {
  outline: none;
}

#greet-input {
  margin-right: 5px;
}


</style>
