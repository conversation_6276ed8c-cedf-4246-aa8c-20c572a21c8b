// Learn more about <PERSON><PERSON> commands at https://tauri.app/develop/calling-rust/
use tauri::{Manager, WindowEvent};

#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
fn close_window(window: tauri::Window) -> Result<(), String> {
    window.close().map_err(|e| e.to_string())
}

#[tauri::command]
fn maximize_window(window: tauri::Window) -> Result<(), String> {
    let fullscreen = window.is_fullscreen().map_err(|e| e.to_string())?;
    
    if !fullscreen {
        // 进入全屏前的准备工作
        window.set_decorations(false).map_err(|e| e.to_string())?;
        
        // 添加延迟，给前端UI准备时间
        std::thread::sleep(std::time::Duration::from_millis(50));
    }
    
    // 执行全屏切换
    window.set_fullscreen(!fullscreen).map_err(|e| e.to_string())?;
    
    if !fullscreen {
        // 进入全屏后的额外处理
        std::thread::sleep(std::time::Duration::from_millis(30));
    }
    
    Ok(())
}

#[tauri::command]
fn toggle_fullscreen(window: tauri::Window) -> Result<(), String> {
    let fullscreen = window.is_fullscreen().map_err(|e| e.to_string())?;
    window.set_fullscreen(!fullscreen).map_err(|e| e.to_string())
}

#[tauri::command]
fn minimize_window(window: tauri::Window) -> Result<(), String> {
    window.minimize().map_err(|e| e.to_string())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .setup(|app| {
            // 获取主窗口并设置事件监听器
            if let Some(window) = app.get_webview_window("main") {
                let app_handle = app.handle().clone();
                window.on_window_event(move |event| {
                    if let WindowEvent::CloseRequested { .. } = event {
                        // 允许正常的关闭行为
                        app_handle.exit(0);
                    }
                });
            }
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![greet, close_window, maximize_window, minimize_window, toggle_fullscreen])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
