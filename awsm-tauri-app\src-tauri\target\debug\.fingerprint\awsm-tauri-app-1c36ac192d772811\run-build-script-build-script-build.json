{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[4504047264575932426, "build_script_build", false, 11499334086599800616], [12092653563678505622, "build_script_build", false, 8660128768135603923], [16702348383442838006, "build_script_build", false, 7299570001123890745]], "local": [{"RerunIfChanged": {"output": "debug\\build\\awsm-tauri-app-1c36ac192d772811\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}