{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 4299523277342534766, "profile": 8731458305071235362, "path": 10763286916239946207, "deps": [[4504047264575932426, "build_script_build", false, 16339220395926813921], [8569119365930580996, "serde_json", false, 5022011544372123778], [9689903380558560274, "serde", false, 12027688467576510606], [12092653563678505622, "tauri", false, 17313102082654125826], [16702348383442838006, "tauri_plugin_opener", false, 17682007137437708862]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\awsm-tauri-app-8e0bb71731db3e2e\\dep-lib-awsm_tauri_app_lib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}