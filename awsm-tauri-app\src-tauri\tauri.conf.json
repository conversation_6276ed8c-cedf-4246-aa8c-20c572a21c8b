{"$schema": "https://schema.tauri.app/config/2", "productName": "awsm-tauri-app", "version": "0.1.0", "identifier": "com.awsm-tauri-app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:5004", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "awsm-tauri-app", "width": 800, "height": 600, "decorations": false, "maximizable": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}